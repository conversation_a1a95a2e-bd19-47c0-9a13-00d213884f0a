import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:health_diary/pages/home/<USER>/recent_records_controller.dart';

void main() {
  group('RecentRecordsController', () {
    test('should create RecentRecordsQueryData correctly', () {
      const queryData = (page: 1, pageSize: 10);
      
      expect(queryData.page, equals(1));
      expect(queryData.pageSize, equals(10));
    });

    test('should create RecentRecordsResponse correctly', () {
      const response = RecentRecordsResponse(
        records: [],
        totalCount: 0,
        currentPage: 1,
        hasMore: false,
      );
      
      expect(response.records, isEmpty);
      expect(response.totalCount, equals(0));
      expect(response.currentPage, equals(1));
      expect(response.hasMore, isFalse);
    });
  });
}
