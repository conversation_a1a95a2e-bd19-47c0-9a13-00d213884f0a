import 'dart:async';
import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../repository/blood_pressure_repository.dart';
import '../../../repository/blood_sugar_repository.dart';
import '../../../repository/database.dart';
import '../../../types/health_record.dart';
import '../../../types/health_types.dart';

part 'recent_records_controller.g.dart';

/// 分页查询数据
typedef RecentRecordsQueryData = ({int page, int pageSize});

/// 最近记录响应数据
class RecentRecordsResponse {
  final List<HealthRecordEntry> records;
  final int totalCount;
  final int currentPage;
  final bool hasMore;

  const RecentRecordsResponse({
    required this.records,
    required this.totalCount,
    required this.currentPage,
    required this.hasMore,
  });
}

/// 最近记录控制器
@riverpod
class RecentRecordsController extends _$RecentRecordsController {
  static const int defaultPageSize = 10;

  /// 缓存超时时间
  Timer? _cacheTimer;

  @override
  Future<RecentRecordsResponse> build({
    required RecentRecordsQueryData queryData,
  }) async {
    // 设置缓存超时
    _setupCacheTimeout();

    // 获取所有记录并合并
    final allRecords = await _getAllRecords();

    // 分页处理
    final startIndex = (queryData.page - 1) * queryData.pageSize;
    final endIndex = startIndex + queryData.pageSize;

    final pagedRecords = allRecords.length > startIndex
        ? allRecords.sublist(
            startIndex,
            endIndex > allRecords.length ? allRecords.length : endIndex
          )
        : <HealthRecordEntry>[];

    return RecentRecordsResponse(
      records: pagedRecords,
      totalCount: allRecords.length,
      currentPage: queryData.page,
      hasMore: endIndex < allRecords.length,
    );
  }

  /// 获取所有记录并合并排序
  Future<List<HealthRecordEntry>> _getAllRecords() async {
    try {
      // 并行获取血压和血糖记录
      final results = await Future.wait([
        _getBloodPressureRecords(),
        _getBloodSugarRecords(),
      ]);

      final allRecords = <HealthRecordEntry>[];
      allRecords.addAll(results[0]);
      allRecords.addAll(results[1]);

      // 按创建时间降序排序
      allRecords.sort((a, b) => _parseTime(b.time).compareTo(_parseTime(a.time)));

      return allRecords;
    } catch (e) {
      log('Error getting all records: $e');
      return [];
    }
  }

  /// 设置缓存超时
  void _setupCacheTimeout() {
    // 当 provider 被销毁时，取消定时器
    ref.onDispose(() {
      _cacheTimer?.cancel();
    });

    // 保持缓存活跃
    final link = ref.keepAlive();

    // 当最后一个监听者被移除时，启动定时器
    ref.onCancel(() {
      _cacheTimer = Timer(const Duration(seconds: 30), () {
        // 超时后释放缓存数据
        link.close();
      });
    });

    // 如果 provider 重新被监听，取消定时器
    ref.onResume(() {
      _cacheTimer?.cancel();
    });
  }

  /// 获取血压记录
  Future<List<HealthRecordEntry>> _getBloodPressureRecords() async {
    try {
      final repository = ref.read(bloodPressureRepositoryProvider);
      // 使用 Stream 转换为 Future，获取最新数据
      final records = await repository.getAllBloodPressures().first;

      return records.map((record) => _convertBloodPressureToHealthRecord(record)).toList();
    } catch (e) {
      log('Error fetching blood pressure records: $e');
      return [];
    }
  }

  /// 获取血糖记录
  Future<List<HealthRecordEntry>> _getBloodSugarRecords() async {
    try {
      final repository = ref.read(bloodSugarRepositoryProvider);
      // 使用 Stream 转换为 Future，获取最新数据
      final records = await repository.getAllBloodSugars().first;

      return records.map((record) => _convertBloodSugarToHealthRecord(record)).toList();
    } catch (e) {
      log('Error fetching blood sugar records: $e');
      return [];
    }
  }

  /// 将血压记录转换为健康记录条目
  HealthRecordEntry _convertBloodPressureToHealthRecord(BloodPressure record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_pressure'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodPressure,
      color: 0xFFFFEBF0,
      values: [
        HealthRecordValue(
          label: 'systolic'.tr(),
          value: record.systolic.toString(),
          unit: 'mmHg',
        ),
        HealthRecordValue(
          label: 'diastolic'.tr(),
          value: record.diastolic.toString(),
          unit: 'mmHg',
        ),
        if (record.pulse != null)
          HealthRecordValue(
            label: 'pulse'.tr(),
            value: record.pulse.toString(),
            unit: 'bpm',
          ),
      ],
    );
  }

  /// 将血糖记录转换为健康记录条目
  HealthRecordEntry _convertBloodSugarToHealthRecord(BloodSugar record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_sugar'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodSugar,
      color: 0xFFE4F4FF,
      values: [
        HealthRecordValue(
          label: 'blood_sugar'.tr(),
          value: record.value.toStringAsFixed(1),
          unit: 'mmol/L',
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final recordDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    final timeFormat = DateFormat('HH:mm');
    final timeString = timeFormat.format(dateTime);

    if (recordDate == today) {
      return '${'today'.tr()} $timeString';
    } else if (recordDate == yesterday) {
      return '${'yesterday'.tr()} $timeString';
    } else {
      final dateFormat = DateFormat('MM-dd');
      return '${dateFormat.format(dateTime)} $timeString';
    }
  }

  /// 解析时间字符串为 DateTime 用于排序
  DateTime _parseTime(String timeString) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);

    try {
      if (timeString.contains('today'.tr()) || timeString.contains('今天')) {
        final timeMatch = RegExp(r'(\d{2}):(\d{2})').firstMatch(timeString);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          return today.add(Duration(hours: hour, minutes: minute));
        }
      } else if (timeString.contains('yesterday'.tr()) || timeString.contains('昨天')) {
        final timeMatch = RegExp(r'(\d{2}):(\d{2})').firstMatch(timeString);
        if (timeMatch != null) {
          final hour = int.parse(timeMatch.group(1)!);
          final minute = int.parse(timeMatch.group(2)!);
          return today.subtract(const Duration(days: 1)).add(Duration(hours: hour, minutes: minute));
        }
      } else {
        // 尝试解析 MM-dd HH:mm 格式
        final dateTimeMatch = RegExp(r'(\d{2})-(\d{2}) (\d{2}):(\d{2})').firstMatch(timeString);
        if (dateTimeMatch != null) {
          final month = int.parse(dateTimeMatch.group(1)!);
          final day = int.parse(dateTimeMatch.group(2)!);
          final hour = int.parse(dateTimeMatch.group(3)!);
          final minute = int.parse(dateTimeMatch.group(4)!);
          return DateTime(now.year, month, day, hour, minute);
        }
      }
    } catch (e) {
      log('Error parsing time string: $timeString, error: $e');
    }

    // 如果解析失败，返回当前时间
    return now;
  }

  /// 刷新数据
  void refresh() {
    ref.invalidateSelf();
  }
}
