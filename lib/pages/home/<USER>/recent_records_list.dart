import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../themes/app_theme.dart';
import '../../../types/health_record.dart';
import '../controller/recent_records_controller.dart';
import 'data_item_widget.dart';

/// 最近记录列表
class RecentRecordsList extends StatelessWidget {
  final List<HealthRecordEntry> records;

  const RecentRecordsList({super.key, required this.records});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'recent_records'.tr(),
                style: Theme.of(context).textTheme.titleLarge,
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        ListView.builder(
          padding: EdgeInsets.zero,
          shrinkWrap: true,

          physics: const NeverScrollableScrollPhysics(),
          itemCount: records.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: EdgeInsets.only(bottom: 12),
              child: RecordItem(record: records[index]),
            );
          },
        ),
      ],
    );
  }
}

/// 根据记录颜色值获取对应的主题颜色
Color _getRecordCardColor(BuildContext context, int colorValue) {
  switch (colorValue) {
    case 0xFFFFEBF0:
      return context.appColors.bloodPressureCard;
    case 0xFFE4F4FF:
      return context.appColors.bloodSugarCard;
    default:
      return Color(colorValue);
  }
}

/// 记录条目
class RecordItem extends StatelessWidget {
  final HealthRecordEntry record;

  const RecordItem({super.key, required this.record});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getRecordCardColor(context, record.color),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // 顶部：左上角时间，右上角记录类型
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _getLocalizedType(record.type),
                style: Theme.of(context).textTheme.titleMedium,
              ),
              Text(
                _getLocalizedTime(record.time),
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 12),
          // 数据显示区域
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: record.values.map((value) => DataItemWidget(
              label: _getLocalizedLabel(value.label),
              value: value.value,
              unit: value.unit,
            )).toList(),
          ),
        ],
      ),
    );
  }

  String _getLocalizedType(String type) {
    switch (type) {
      case '血压':
        return 'blood_pressure'.tr();
      case '血糖':
        return 'blood_sugar'.tr();
      default:
        return type;
    }
  }

  String _getLocalizedTime(String time) {
    switch (time) {
      case '今天 08:30':
        return 'today_time'.tr();
      case '昨天 18:45':
        return 'yesterday_evening'.tr();
      case '昨天 08:15':
        return 'yesterday_morning'.tr();
      default:
        return time;
    }
  }

  String _getLocalizedLabel(String label) {
    switch (label) {
      case '高压':
        return 'systolic'.tr();
      case '低压':
        return 'diastolic'.tr();
      case '脉搏':
        return 'pulse'.tr();
      case '血糖':
        return 'blood_sugar'.tr();
      default:
        return label;
    }
  }
}
