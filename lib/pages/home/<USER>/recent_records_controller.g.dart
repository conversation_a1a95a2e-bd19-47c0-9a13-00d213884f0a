// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'recent_records_controller.dart';

// **************************************************************************
// RiverpodGenerator
// **************************************************************************

String _$recentRecordsControllerHash() =>
    r'4ceccdf4430ef6f9b54d4bb07d2f8687e3462442';

/// Copied from Dart SDK
class _SystemHash {
  _SystemHash._();

  static int combine(int hash, int value) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + value);
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x0007ffff & hash) << 10));
    return hash ^ (hash >> 6);
  }

  static int finish(int hash) {
    // ignore: parameter_assignments
    hash = 0x1fffffff & (hash + ((0x03ffffff & hash) << 3));
    // ignore: parameter_assignments
    hash = hash ^ (hash >> 11);
    return 0x1fffffff & (hash + ((0x00003fff & hash) << 15));
  }
}

abstract class _$RecentRecordsController
    extends BuildlessAutoDisposeAsyncNotifier<RecentRecordsResponse> {
  late final ({int page, int pageSize}) queryData;

  FutureOr<RecentRecordsResponse> build({
    required ({int page, int pageSize}) queryData,
  });
}

/// 最近记录控制器
///
/// Copied from [RecentRecordsController].
@ProviderFor(RecentRecordsController)
const recentRecordsControllerProvider = RecentRecordsControllerFamily();

/// 最近记录控制器
///
/// Copied from [RecentRecordsController].
class RecentRecordsControllerFamily
    extends Family<AsyncValue<RecentRecordsResponse>> {
  /// 最近记录控制器
  ///
  /// Copied from [RecentRecordsController].
  const RecentRecordsControllerFamily();

  /// 最近记录控制器
  ///
  /// Copied from [RecentRecordsController].
  RecentRecordsControllerProvider call({
    required ({int page, int pageSize}) queryData,
  }) {
    return RecentRecordsControllerProvider(queryData: queryData);
  }

  @override
  RecentRecordsControllerProvider getProviderOverride(
    covariant RecentRecordsControllerProvider provider,
  ) {
    return call(queryData: provider.queryData);
  }

  static const Iterable<ProviderOrFamily>? _dependencies = null;

  @override
  Iterable<ProviderOrFamily>? get dependencies => _dependencies;

  static const Iterable<ProviderOrFamily>? _allTransitiveDependencies = null;

  @override
  Iterable<ProviderOrFamily>? get allTransitiveDependencies =>
      _allTransitiveDependencies;

  @override
  String? get name => r'recentRecordsControllerProvider';
}

/// 最近记录控制器
///
/// Copied from [RecentRecordsController].
class RecentRecordsControllerProvider
    extends
        AutoDisposeAsyncNotifierProviderImpl<
          RecentRecordsController,
          RecentRecordsResponse
        > {
  /// 最近记录控制器
  ///
  /// Copied from [RecentRecordsController].
  RecentRecordsControllerProvider({
    required ({int page, int pageSize}) queryData,
  }) : this._internal(
         () => RecentRecordsController()..queryData = queryData,
         from: recentRecordsControllerProvider,
         name: r'recentRecordsControllerProvider',
         debugGetCreateSourceHash: const bool.fromEnvironment('dart.vm.product')
             ? null
             : _$recentRecordsControllerHash,
         dependencies: RecentRecordsControllerFamily._dependencies,
         allTransitiveDependencies:
             RecentRecordsControllerFamily._allTransitiveDependencies,
         queryData: queryData,
       );

  RecentRecordsControllerProvider._internal(
    super._createNotifier, {
    required super.name,
    required super.dependencies,
    required super.allTransitiveDependencies,
    required super.debugGetCreateSourceHash,
    required super.from,
    required this.queryData,
  }) : super.internal();

  final ({int page, int pageSize}) queryData;

  @override
  FutureOr<RecentRecordsResponse> runNotifierBuild(
    covariant RecentRecordsController notifier,
  ) {
    return notifier.build(queryData: queryData);
  }

  @override
  Override overrideWith(RecentRecordsController Function() create) {
    return ProviderOverride(
      origin: this,
      override: RecentRecordsControllerProvider._internal(
        () => create()..queryData = queryData,
        from: from,
        name: null,
        dependencies: null,
        allTransitiveDependencies: null,
        debugGetCreateSourceHash: null,
        queryData: queryData,
      ),
    );
  }

  @override
  AutoDisposeAsyncNotifierProviderElement<
    RecentRecordsController,
    RecentRecordsResponse
  >
  createElement() {
    return _RecentRecordsControllerProviderElement(this);
  }

  @override
  bool operator ==(Object other) {
    return other is RecentRecordsControllerProvider &&
        other.queryData == queryData;
  }

  @override
  int get hashCode {
    var hash = _SystemHash.combine(0, runtimeType.hashCode);
    hash = _SystemHash.combine(hash, queryData.hashCode);

    return _SystemHash.finish(hash);
  }
}

@Deprecated('Will be removed in 3.0. Use Ref instead')
// ignore: unused_element
mixin RecentRecordsControllerRef
    on AutoDisposeAsyncNotifierProviderRef<RecentRecordsResponse> {
  /// The parameter `queryData` of this provider.
  ({int page, int pageSize}) get queryData;
}

class _RecentRecordsControllerProviderElement
    extends
        AutoDisposeAsyncNotifierProviderElement<
          RecentRecordsController,
          RecentRecordsResponse
        >
    with RecentRecordsControllerRef {
  _RecentRecordsControllerProviderElement(super.provider);

  @override
  ({int page, int pageSize}) get queryData =>
      (origin as RecentRecordsControllerProvider).queryData;
}

// ignore_for_file: type=lint
// ignore_for_file: subtype_of_sealed_class, invalid_use_of_internal_member, invalid_use_of_visible_for_testing_member, deprecated_member_use_from_same_package
